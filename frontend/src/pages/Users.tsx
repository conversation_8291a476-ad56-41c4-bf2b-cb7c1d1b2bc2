import React, { useState } from 'react';
import {
  Table,
  Typography,
  Card,
  Tag,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Popconfirm,
  message,
  Tabs,
  Alert
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  UserOutlined,
  DeleteOutlined,
  TeamOutlined,
  ToolOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import type { User, Role, SkillGroup, CreateUserRequest } from '@/types/api';
import RoleManagement from '@/components/RoleManagement';
import SkillGroupManagement from '@/components/SkillGroupManagement';
import dayjs from 'dayjs';

const { Title } = Typography;
const { TabPane } = Tabs;

const Users: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  // 角色名称中文映射
  const getRoleDisplayName = (roleName: string) => {
    const roleNameMap: Record<string, string> = {
      'admin': '系统管理员',
      'process_engineer': '工艺工程师',
      'planner': '生产计划员',
      'operator': '操作员',
      'quality_inspector': '质量检验员',
    };
    return roleNameMap[roleName] || roleName;
  };

  // 技能组名称中文映射
  const getSkillGroupDisplayName = (groupName: string) => {
    const skillGroupNameMap: Record<string, string> = {
      'CNC Machining': 'CNC加工',
      'Milling': '铣削加工',
      'Turning': '车削加工',
      'Grinding': '磨削加工',
      'Assembly': '装配',
      'Quality Control': '质量控制',
    };
    return skillGroupNameMap[groupName] || groupName;
  };

  const { data: users, isLoading } = useQuery(
    'users',
    () => apiClient.getUsers()
  );

  const { data: roles = [] } = useQuery(
    'roles',
    () => apiClient.getRoles()
  );

  const { data: skillGroups = [] } = useQuery(
    'skill-groups',
    () => apiClient.getSkillGroups()
  );

  const createMutation = useMutation(
    (data: CreateUserRequest) => apiClient.createUser(data),
    {
      onSuccess: () => {
        message.success('用户创建成功');
        queryClient.invalidateQueries('users');
        setIsModalVisible(false);
        form.resetFields();
      },
      onError: () => {
        message.error('用户创建失败');
      },
    }
  );

  const updateStatusMutation = useMutation(
    ({ id, isActive }: { id: number; isActive: boolean }) =>
      apiClient.updateUserStatus(id, isActive),
    {
      onSuccess: () => {
        message.success('用户状态更新成功');
        queryClient.invalidateQueries('users');
      },
      onError: () => {
        message.error('用户状态更新失败');
      },
    }
  );

  const deleteMutation = useMutation(
    (id: number) => apiClient.deleteUser(id),
    {
      onSuccess: () => {
        message.success('用户删除成功');
        queryClient.invalidateQueries('users');
      },
      onError: () => {
        message.error('用户删除失败');
      },
    }
  );

  const getStatusTag = (isActive: boolean) => {
    return isActive ?
      <Tag color="green">活跃</Tag> :
      <Tag color="red">禁用</Tag>;
  };

  const handleCreate = () => {
    setEditingUser(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (user: User) => {
    setEditingUser(user);
    setIsModalVisible(true);

    // 确保正确映射用户的角色和技能组ID
    const userRoleIds = roles.filter(role => user.roles?.includes(role.role_name)).map(role => role.id);
    const userSkillGroupIds = skillGroups.filter(skill => user.skills?.includes(skill.group_name)).map(skill => skill.id);

    form.setFieldsValue({
      username: user.username,
      full_name: user.full_name,
      role_ids: userRoleIds,
      skill_group_ids: userSkillGroupIds,
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('提交的表单数据:', values);

      if (editingUser) {
        // 编辑模式 - 分别更新角色和技能
        console.log('更新用户角色:', editingUser.id, values.role_ids);
        console.log('更新用户技能组:', editingUser.id, values.skill_group_ids);

        await apiClient.updateUserRoles(editingUser.id, values.role_ids || []);
        await apiClient.updateUserSkills(editingUser.id, values.skill_group_ids || []);
        message.success('用户信息更新成功');
        queryClient.invalidateQueries('users');
      } else {
        // 创建模式
        console.log('创建新用户:', values);
        createMutation.mutate(values);
      }
      setIsModalVisible(false);
      setEditingUser(null);
      form.resetFields();
    } catch (error) {
      console.error('操作失败:', error);
      message.error('操作失败，请检查输入信息');
    }
  };

  const handleStatusToggle = (user: User) => {
    updateStatusMutation.mutate({ id: user.id, isActive: !user.is_active });
  };

  const handleDelete = (user: User) => {
    deleteMutation.mutate(user.id);
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (text: string) => <strong>{text}</strong>,
    },
    {
      title: '姓名',
      dataIndex: 'full_name',
      key: 'full_name',
      render: (text: string) => text || '未设置',
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean, record: User) => (
        <Space>
          {getStatusTag(isActive)}
          <Switch
            size="small"
            checked={isActive}
            onChange={() => handleStatusToggle(record)}
            loading={updateStatusMutation.isLoading}
          />
        </Space>
      ),
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      render: (roles: string[]) => (
        <Space>
          {roles?.map((role, index) => (
            <Tag key={index} color="blue">{getRoleDisplayName(role)}</Tag>
          )) || '无角色'}
        </Space>
      ),
    },
    {
      title: '技能',
      dataIndex: 'skills',
      key: 'skills',
      render: (skills: string[]) => (
        <Space>
          {skills?.slice(0, 2).map((skill, index) => (
            <Tag key={index} color="cyan">{getSkillGroupDisplayName(skill)}</Tag>
          )) || '无技能'}
          {skills?.length > 2 && <Tag>+{skills.length - 2}</Tag>}
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: any, record: User) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个用户吗？"
            description="删除后无法恢复"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              loading={deleteMutation.isLoading}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div className="page-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={2} style={{ margin: 0 }}>
              用户管理
            </Title>
            <Typography.Paragraph style={{ margin: 0, color: '#666' }}>
              管理系统用户、分配角色和技能组权限
            </Typography.Paragraph>
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreate}
          >
            新建用户
          </Button>
        </div>
      </div>

      {/* 用户统计卡片 */}
      <div style={{ marginBottom: 24 }}>
        <Space size="large">
          <Card size="small" style={{ minWidth: 120 }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                {users?.length || 0}
              </div>
              <div style={{ color: '#666' }}>总用户数</div>
            </div>
          </Card>
          <Card size="small" style={{ minWidth: 120 }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                {users?.filter(user => user.skills && user.skills.length > 0).length || 0}
              </div>
              <div style={{ color: '#666' }}>已分配技能</div>
            </div>
          </Card>
          <Card size="small" style={{ minWidth: 120 }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#ff4d4f' }}>
                {users?.filter(user => !user.skills || user.skills.length === 0).length || 0}
              </div>
              <div style={{ color: '#666' }}>未分配技能</div>
            </div>
          </Card>
          <Card size="small" style={{ minWidth: 120 }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                {users?.filter(u => u.is_active).length || 0}
              </div>
              <div style={{ color: '#666' }}>活跃用户</div>
            </div>
          </Card>
          <Card size="small" style={{ minWidth: 120 }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#faad14' }}>
                {roles?.length || 0}
              </div>
              <div style={{ color: '#666' }}>系统角色</div>
            </div>
          </Card>
          <Card size="small" style={{ minWidth: 120 }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#722ed1' }}>
                {skillGroups?.length || 0}
              </div>
              <div style={{ color: '#666' }}>技能组</div>
            </div>
          </Card>
        </Space>
      </div>

      {/* 未分配技能组的用户警告 */}
      {users && users.filter(user => !user.skills || user.skills.length === 0).length > 0 && (
        <Alert
          message="注意：有用户未分配技能组"
          description={
            <div>
              <p>以下用户没有分配技能组，无法查看和执行任务：</p>
              <div style={{ marginTop: 8 }}>
                {users.filter(user => !user.skills || user.skills.length === 0).map(user => (
                  <Tag key={user.id} color="orange" style={{ marginBottom: 4 }}>
                    {user.username} ({user.full_name || '未设置姓名'})
                  </Tag>
                ))}
              </div>
              <p style={{ marginTop: 8, marginBottom: 0 }}>
                请点击"编辑"按钮为这些用户分配适当的技能组。
              </p>
            </div>
          }
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Tabs defaultActiveKey="users">
        <TabPane tab={<span><UserOutlined />用户管理</span>} key="users">
          <Card>
            <Table
              columns={columns}
              dataSource={users}
              rowKey="id"
              loading={isLoading}
              pagination={{
                total: users?.length || 0,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab={<span><TeamOutlined />角色管理</span>} key="roles">
          <Card>
            <RoleManagement
              onRoleChange={() => {
                queryClient.invalidateQueries('roles');
                queryClient.invalidateQueries('users');
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab={<span><ToolOutlined />技能组管理</span>} key="skills">
          <Card>
            <SkillGroupManagement
              onSkillGroupChange={() => {
                queryClient.invalidateQueries('skill-groups');
                queryClient.invalidateQueries('users');
              }}
            />
          </Card>
        </TabPane>
      </Tabs>

      <Modal
        title={editingUser ? '编辑用户' : '新建用户'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingUser(null);
          form.resetFields();
        }}
        confirmLoading={createMutation.isLoading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            role_ids: [],
            skill_group_ids: [],
          }}
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' },
            ]}
          >
            <Input placeholder="请输入用户名" disabled={!!editingUser} />
          </Form.Item>

          {!editingUser && (
            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' },
              ]}
            >
              <Input.Password placeholder="请输入密码" />
            </Form.Item>
          )}

          <Form.Item
            name="full_name"
            label="姓名"
          >
            <Input placeholder="请输入真实姓名（可选）" />
          </Form.Item>

          <Form.Item
            name="role_ids"
            label="角色"
            rules={[{ required: true, message: '请选择至少一个角色' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择角色"
              options={roles.map(role => ({
                value: role.id,
                label: getRoleDisplayName(role.role_name),
              }))}
            />
          </Form.Item>

          <Form.Item
            name="skill_group_ids"
            label="技能组"
            rules={[{ required: true, message: '请选择至少一个技能组' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择技能组"
              options={skillGroups.map(skill => ({
                value: skill.id,
                label: getSkillGroupDisplayName(skill.group_name),
              }))}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Users;
